# ninja log v5
7	51	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/armeabi-v7a/CMakeFiles/cmake.verify_globs	e10e5510de39601e
46	3489	7751304573097022	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	6b6071dacae158a5
236	4192	7751304579436462	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	cc724561259ffc36
58	4355	7751304581971721	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSISerializer.cpp.o	4086adff64b1ba48
80	5209	7751304589873202	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	4d82791b992d258
69	6131	7751304598532407	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSLogger.cpp.o	af1776b4cd2350fa
13	6370	7751304600099902	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	6884b66371893dbf
34	6626	7751304602596977	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/SharedItems/Shareables.cpp.o	57594ef2ec50f32d
6	7277	7751304608638760	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o	48e29a671338a2dd
3526	9165	7751304628787736	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSScheduler.cpp.o	b41ac2792e599b4
4204	9184	7751304627816140	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/UIScheduler.cpp.o	b74270e833cc61c2
5210	9796	7751304634794499	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o	4019aa01e779c90
91	10026	7751304636717320	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o	cf9a4c33e9c8430f
4356	10055	7751304638699915	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	91472af1e3451f0f
10027	12256	7751304660798822	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	6fc7cd088a4988b1
6371	12403	7751304660718303	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	d23396bbd8b47ee5
6153	12469	7751304662421132	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	1d478e666adce659
6694	15704	7751304693670694	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	f6ed665840fc7c2e
24	16327	7751304701378350	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	7da89cf4ff735f88
9165	16670	7751304704666491	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	48c8c4f4c1a7454
12256	17797	7751304716392484	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o	158b87b708984d4e
7320	18099	7751304718552935	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	89b763cf5168008d
12421	19519	7751304732458960	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b3753698fca1a6c32856249ade94ea38/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o	a2f71d786a4208a3
10055	21826	7751304756489273	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o	d870dff1f8e06f55
18109	23505	7751304773399765	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b3753698fca1a6c32856249ade94ea38/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o	7d5f5b1356c1ec3
9184	26335	7751304800953506	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	80ad4b51da29dfaa
9808	26801	7751304805523952	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	17f1ebe22f7208a6
26805	27407	7751304812075969	../../../../build/intermediates/cxx/Debug/6f6s2k27/obj/armeabi-v7a/libworklets.so	6dfd6428d23d4cbe
26347	28399	7751304822433298	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o	cf274a3fae76caac
21827	29137	7751304829185527	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o	25a1d57269dd9bcc
16671	29695	7751304834871145	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o	967bddad92430161
15721	30295	7751304840397112	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o	1f4e39e972784b3c
16344	31558	7751304852421355	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o	51d301eb2bf10e74
19535	32203	7751304859678713	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b3753698fca1a6c32856249ade94ea38/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o	917e39fc1f9c6fb2
12470	33422	7751304871515310	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b3753698fca1a6c32856249ade94ea38/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o	c444ce51e707ca0d
27407	35286	7751304890623266	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	bda6cc772a4c3b31
28399	35435	7751304892148940	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	a54d587e2aa8c01d
23506	36640	7751304904010225	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o	bf77f800d67830fa
29719	39916	7751304936288249	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	f7eee61a556ffb46
17798	40523	7751304942787627	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o	be2582e91dddb402
29154	43525	7751304972474092	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	c4a852e09844d392
43530	43978	7751304977615907	../../../../build/intermediates/cxx/Debug/6f6s2k27/obj/armeabi-v7a/libreanimated.so	fe3bcdd432920637
2	57	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/armeabi-v7a/CMakeFiles/cmake.verify_globs	e10e5510de39601e
5	111	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/armeabi-v7a/CMakeFiles/cmake.verify_globs	e10e5510de39601e
3	83	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/armeabi-v7a/CMakeFiles/cmake.verify_globs	e10e5510de39601e
4	71	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/Debug/6f6s2k27/armeabi-v7a/CMakeFiles/cmake.verify_globs	e10e5510de39601e
